package com.huike.nova.service.common;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.huike.nova.service.domain.model.common.ConfigChangedItemModel;
import com.purgeteam.dynamic.config.starter.event.ActionConfigEvent;
import lombok.val;

import javax.annotation.Nonnull;
import java.util.Map;

/**
 * 配置工具类
 *
 * <AUTHOR> (<EMAIL>)
 * @version ActionConfigUtil.java, v1.0 11/22/2023 10:53 John Exp$
 */
public class ActionConfigUtil {

    /**
     * 从配置改变事件中获得修改过的配置
     *
     * @param event 配置改变事件
     * @return 配置改变映射: 配置Key-修改内容
     */
    public static Map<String, ConfigChangedItemModel> getChangedConfig(@Nonnull ActionConfigEvent event) {
        val json = JSONObject.toJSONString(event.getPropertyMap());
        return JSONObject.parseObject(json, new TypeReference<Map<String, ConfigChangedItemModel>>() {});
    }

}
