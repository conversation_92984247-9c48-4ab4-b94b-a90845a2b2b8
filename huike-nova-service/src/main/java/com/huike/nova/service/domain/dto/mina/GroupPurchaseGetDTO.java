package com.huike.nova.service.domain.dto.mina;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023年06月08日 16:34
 */
@Data
public class GroupPurchaseGetDTO {

    /**
     * 商品组列表
     */
    @JSONField(name = "commodity")
    private String list;

    /**
     * 限制规则
     */
    @JSONField(name = "limit_rule")
    private String limitRule;

    /**
     * 环境图
     * 在product_attrs中的environment_image_list
     */
    private String environmentImageList;

    /**
     * 菜品图
     * 在product_attrs中的dishes_image_list
     */
    private String dishesImageList;
}
