/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.domain.model.oem.agent.agentpackage;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version FindAgentPackageDetailModel.java, v 0.1 2023-08-07 5:44 PM ruanzy
 */
@Data
public class FindAgentPackageDetailModel {

    /**
     * 套餐ID
     */
    private String packageId;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 套餐数量
     */
    private Integer number;

    /**
     * 1-帐套 2-条数
     */
    private Integer packageType;

    /**
     * 套餐价格
     */
    private BigDecimal price;

    /**
     * 剪辑数量
     */
    private Integer clipNumber;
}