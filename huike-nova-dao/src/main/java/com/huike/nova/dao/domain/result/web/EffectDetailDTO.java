/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.dao.domain.result.web;

import lombok.Data;

/**
 * <AUTHOR>
 * @version EffectDetailResponse.java, v 0.1 2023-05-19 3:36 PM ruanzy
 */
@Data
public class EffectDetailDTO {

    /**
     * 业务id
     */
    private String businessId;

    /**
     * 显示url
     */
    private String showUrl;

    /**
     * 是否默认 1：选择 2：不选择
     */
    private Integer isChoose;

    /**
     * 文字的颜色
     */
    private String fontColor;

    /**
     * 背景颜色
     */
    private String backColour;

    /**
     * 描边颜色
     */
    private String outlineColour;

    /**
     * 描边宽度
     */
    private Integer outline;

    /**
     * 阴影宽度
     */
    private Integer shadow;

    /**
     * 文字间距
     */
    private Integer spacing;

    /**
     * 边框样式
     */
    private Integer borderStyle;

    /**
     * 颜色
     */
    private String color;

    /**
     * 阴影
     */
    private String textShadow;

    /**
     * 描边
     */
    private String textStroke;

    /**
     * 背景色
     */
    private String backgroundColor;

}