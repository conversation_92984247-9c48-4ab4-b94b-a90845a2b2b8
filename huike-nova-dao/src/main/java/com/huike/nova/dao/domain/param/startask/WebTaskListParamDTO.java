package com.huike.nova.dao.domain.param.startask;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023年12月10日 15:14
 */
@Data
public class WebTaskListParamDTO {

    /**
     * 任务类型 -1-全部 1-扫码直发，2-云探
     */
    private Integer taskType;

    /**
     * 任务标题
     */
    private String taskTitle;

    /**
     * -1-全部 1-待支付 2-平台审核中 3-平台审核驳回 4-进行中 5-结算中 6-已完成 7-已取消
     */
    private Integer taskStatus;

    /**
     * 商家账号
     */
    private String phoneNumber;
    /**
     * 小程序id
     */
    private String appletId;

    /**
     * 开始时间
     */
    private String createStartDate;

    /**
     * 结束时间
     */
    private String createEndDate;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 审核开始时间
     */
    private String auditStartDate;

    /**
     * 审核结束时间
     */
    private String auditEndDate;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 发布平台  -1-全部；  1-抖音 ； 2-抖音
     */
    private Integer platform;

    /**
     * 运营记录 -1-全部 1-有记录 2-无记录
     */
    private Integer hasOperatingRecord;

    /**
     * 跟进人id
     */
    private String followUpPersonId;

    /**
     * 是否提前结束任务
     * <p>
     * -1-全部   0-正常结束报名；1-提前结束报名
     */
    private Integer aheadEndApplyFlag;

    /**
     * 代理商id
     */
    private String agentId;
}
