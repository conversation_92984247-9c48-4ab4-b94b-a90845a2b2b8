package com.huike.nova.sdk.meituan.dianping.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 点评三方Bid和门店ID
 * <a href="https://developer.meituan.com/platform/bjx#/document/v2?docId=6000635&rootDocId=5000">接口文档</a>
 *
 * <AUTHOR> (<EMAIL>)
 * @version DianPingThirdPartyBidShopMappingRequest.java, v1.0 08/06/2024 09:07 John Exp$
 */
@Data
@Accessors(chain = true)
public class DianPingThirdPartyBidShopMappingRequest {

    /**
     * 系统授权成功后，点评到综开放平台颁发给应用的授权信息。等同于Token<br/>
     * 注意: 上品和三方码相关的Session是不一样的，三方码的Session是固定的，上品的使用门店授权的
     */
    @JSONField(name = "session")
    private String session;

    /**
     * 客户Id: 授权接口返回
     */
    @JSONField(name = "bid")
    private String bid;

    /**
     * 点评门店Id列表，使用英文逗号分隔
     */
    @JSONField(name = "shopids")
    private String shopIds;

}
