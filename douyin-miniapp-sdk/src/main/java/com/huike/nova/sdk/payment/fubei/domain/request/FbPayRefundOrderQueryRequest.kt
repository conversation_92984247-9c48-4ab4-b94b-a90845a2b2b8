/*
 *
 *  * Fshows Technology
 *  * Copyright (C) 2022-2024 All Rights Reserved.
 *
 */

package com.huike.nova.sdk.payment.fubei.domain.request

import com.alibaba.fastjson.annotation.JSONField

/**
 * 微信支付退款查询
 *
 * <AUTHOR> (<EMAIL>)
 * @version FbPayRefundOrderQueryRequest.java, v1.0 2025/2/24 18:05 John Exp$
 */
class FbPayRefundOrderQueryRequest {
    /**
     * 付呗商户号，以服务商级接入时必传，以商户级接入时不传
     */
    @JSONField(name = "merchant_id")
    var merchantId: String? = null

    /**
     * 付呗退款号，与外部系统退款号不能同时为空（二选一），如果同时存在优先取refund_sn
     */
    @JSONField(name = "refund_sn")
    var refundSn: String? = null

    /**
     * 外部系统退款号，与付呗退款号不能同时为空（二选一）
     */
    @JSONField(name = "merchant_refund_sn")
    var merchantRefundSn: String? = null
}