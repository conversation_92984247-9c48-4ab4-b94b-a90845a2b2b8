/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.common.enums;

/**
 * <AUTHOR>
 * @version ContentTypeEnum.java, v 0.1 2023-03-06 9:55 AM ruanzy
 */
public enum ContentTypeEnum {
    /**
     * 标题文案
     */
    TITLE("标题文案", 1),
    /**
     * 视频顶部文案
     */
    TOP("视频顶部文案", 2),
    ;

    private String name;
    private Integer value;

    ContentTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static ContentTypeEnum getByValue(Integer value) {
        ContentTypeEnum[] valueList = ContentTypeEnum.values();
        for (ContentTypeEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public Integer getValue() {
        return value;
    }
}