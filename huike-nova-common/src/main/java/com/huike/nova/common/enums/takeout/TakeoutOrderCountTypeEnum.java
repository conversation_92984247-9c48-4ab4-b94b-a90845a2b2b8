/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.common.enums.takeout;

/**
 * <AUTHOR>
 * @version TakeoutOrderCountTypeEnum.java, v 0.1 2023-02-02 4:25 PM ruanzy
 */
public enum TakeoutOrderCountTypeEnum {
    /**
     * 成交订单
     */
    DEAL_ORDER("成交订单", "dealOrder"),
    /**
     * 成交人数
     */
    DEAL_COUNT("成交人数", "dealCount"),
    /**
     * 成交金额
     */
    DEAL_AMOUNT("成交金额", "dealAmount"),
    /**
     * 履约订单
     */
    FINISH_ORDER("履约订单", "finishOrder"),
    /**
     * 履约人数
     */
    FINISH_COUNT("履约人数", "finishCount"),
    /**
     * 履约金额
     */
    FINISH_AMOUNT("履约金额", "finishAmount"),
    /**
     * 退款订单
     */
    REFUND_ORDER("退款订单", "refundOrder"),
    /**
     * 退款人数
     */
    REFUND_COUNT("退款人数", "refundCount"),
    /**
     * 退款金额
     */
    REFUND_AMOUNT("退款金额", "refundAmount"),
    ;

    private String name;
    private String value;

    TakeoutOrderCountTypeEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public static TakeoutOrderCountTypeEnum getByValue(String value) {
        TakeoutOrderCountTypeEnum[] valueList = TakeoutOrderCountTypeEnum.values();
        for (TakeoutOrderCountTypeEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public String getValue() {
        return value;
    }
}