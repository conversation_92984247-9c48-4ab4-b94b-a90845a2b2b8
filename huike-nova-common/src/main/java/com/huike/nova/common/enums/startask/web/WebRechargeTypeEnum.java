/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.common.enums.startask.web;

/**
 * <AUTHOR>
 * @version WebRechargeTypeEnum.java, v 0.1 2024-04-17 2:31 PM ruanzy
 */
public enum WebRechargeTypeEnum {

    /**
     * 任务金
     */
    TASK_GOLD("任务金", 1),

    /**
     * 返还奖励金
     */
    RETURN_REWARD("返还奖励金", 2),

    /**
     * 邀请奖励金
     */
    INVITATION_REWARD("邀请奖励金", 3),
    ;

    private String name;
    private Integer value;

    WebRechargeTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static WebRechargeTypeEnum getByValue(Integer value) {
        WebRechargeTypeEnum[] valueList = WebRechargeTypeEnum.values();
        for (WebRechargeTypeEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public Integer getValue() {
        return value;
    }
}