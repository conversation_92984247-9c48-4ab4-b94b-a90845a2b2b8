package com.huike.nova.common.enums.qyk.mina;

import lombok.Getter;

import java.util.Objects;

/**
 * 抖音商圈卡退款自动审核状态
 *
 * <AUTHOR> (<EMAIL>)
 * @version QykDouyinAfterSaleAutoAuditStatusEnum.java, v1.0 2025-04-30 17:40 John Exp$
 */
@Getter
public enum QykDouyinAfterSaleAutoAuditStatusEnum {

    PASS("通过", 1),

    DENIED("拒绝", 2),

    SKIP("跳过", 3)
    ;

    QykDouyinAfterSaleAutoAuditStatusEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    private final String name;

    private final Integer value;

    public static QykDouyinAfterSaleAutoAuditStatusEnum getByCode(Integer num) {
        for (QykDouyinAfterSaleAutoAuditStatusEnum item : QykDouyinAfterSaleAutoAuditStatusEnum.values()) {
            if (Objects.equals(num, item.getValue())) {
                return item;
            }
        }
        return SKIP;
    }
}
