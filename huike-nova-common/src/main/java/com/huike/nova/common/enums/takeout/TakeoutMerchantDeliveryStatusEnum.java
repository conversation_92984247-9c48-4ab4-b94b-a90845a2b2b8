/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.common.enums.takeout;

/**
 * <AUTHOR>
 * @version TakeoutMerchantDeliveryStatusEnum.java, v 0.1 2023-02-02 2:00 PM ruanzy
 */
public enum TakeoutMerchantDeliveryStatusEnum {
    /**
     * 骑手已接单
     */
    RIDER_TAKE_ORDERS("骑手已接单", 102),
    /**
     * 骑手已到店
     */
    RIDER_REACH_STORES("骑手已到店", 103),
    /**
     * 骑手配送中
     */
    RIDER_PICK("骑手配送中", 104),
    /**
     * 骑手已送达
     */
    RIDER_DELIVERY("骑手已送达", 200),
    /**
     * 运单取消
     */
    ORDER_CANCEL("运单取消", 300),
    ;

    private String name;
    private Integer value;

    TakeoutMerchantDeliveryStatusEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static TakeoutMerchantDeliveryStatusEnum getByValue(Integer value) {
        TakeoutMerchantDeliveryStatusEnum[] valueList = TakeoutMerchantDeliveryStatusEnum.values();
        for (TakeoutMerchantDeliveryStatusEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public Integer getValue() {
        return value;
    }
}