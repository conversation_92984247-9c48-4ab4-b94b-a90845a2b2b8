package com.huike.nova.common.enums;/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */

/**
 * 标题类型枚举
 *
 * <AUTHOR>
 */

public enum TitleTypeEnum {
    /**
     * 底部标题
     */
    BOTTOM(1, "底部标题"),
    /**
     * 顶部标题
     */
    TOP(2, "顶部标题");

    private Integer value;

    private String name;

    TitleTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
